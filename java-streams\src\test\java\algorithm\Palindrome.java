package algorithm;

public class Palindrome {
	
	
	public boolean isPalindrome(String str) {
		char[] chars = str.toCharArray();
		int left =0;
		int right = chars.length -1 ;
		
		for(left=0; left < right; left++, right--){
			if(chars[left] != chars[right]) {
				return false;
			}
		}
		return true;
	}
	
	public static void main(String[] args) {
		String str = "sasasdasdfasfda";
		Palindrome obj = new Palindrome();
		boolean isPal = obj.isPalindrome(str);
		System.out.println(isPal);

	}

}
