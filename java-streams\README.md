# Java Streams API
https://amigoscode.com/p/java-streams
![cover](https://user-images.githubusercontent.com/40702606/137604079-5a73276f-a32a-4f03-adca-752f9a01adc2.png)

# Course Description
Functional programming is becoming very popular and it focuses around pure functions. Functional applications avoid the shared state, and tend to be more concise and predictable than those using object-oriented code. In this course <PERSON> will teach you how to move away from imperative to declarative programming allowing you to write less code and focus on what is important when build applications.

## List of topics for this course
- What is functional programming
- Stream API
- Transformations with Map
- Reduce
- Filter
- Collectors
- Statistics
- Grouping
- Parallel steams
- Exercises