package algorithm;

import java.util.Scanner;

public class FibonacciSeries {
	
	
    /*
     * Java program for Fibonacci number using recursion.
     * This program uses tail recursion to calculate Fibonacci number for a given number
     * @return Fibonacci number
     */
    public static int fibonacci(int number){
        if(number == 1 || number == 2){
            return 1;
        }

        return fibonacci(number-1) + fi<PERSON>acci(number -2); //tail recursion
    }



    /*
     * Java program to calculate Fibonacci number using loop or Iteration.
     * @return Fibonacci number
     */
    public static void fibonacci2(int number){
        if(number == 1 || number == 2){
            //return 1;
        	System.out.print("0" + " 1" );
        }
        
        System.out.print("0" + " 1 " + " 1 ");
        
        int fibo1=1, fibo2=1, fibonacci=1;
        for(int i= 3; i<= number; i++){

            //Fibonacci number is sum of previous two Fibonacci number
            fibonacci = fibo1 + fibo2;
            fibo1 = fibo2;
            fibo2 = fibonacci;
            System.out.print(fibonacci + " ");
            

        }
       // return fibonacci; //Fibonacci number

    }
	public static void main(String args[]) {

        //input to print Fibonacci series upto how many numbers
        System.out.println("Enter number upto which Fibonacci series to print: ");
        int number = new Scanner(System.in).nextInt();

        // Solution#1
        System.out.println("Fibonacci series upto " + number +" numbers : ");
        //printing Fibonacci series upto number
        for(int i=1; i<=number; i++){
            System.out.print(fibonacci(i) +" ");
        }
        
        // Solution#2
       // System.out.println("Fibonacci series upto " + number +" numbers : ");
        //printing Fibonacci series upto number
        
        //fibonacci2(number);

    }



}
