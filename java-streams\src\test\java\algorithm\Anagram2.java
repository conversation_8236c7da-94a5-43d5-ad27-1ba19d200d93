package algorithm;

import org.assertj.core.util.Streams;

import java.util.Arrays;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Anagram2 {
	public static void main(String[] args) {
		anagraming("iceman1", "cinema");
		anagraming2("iceman", "cinema");
	}


    public static void anagraming(String a, String b){
      char [] a1 = a.toLowerCase().toCharArray();
      char [] b1 = b.toLowerCase().toCharArray();

      Arrays.sort(a1);
        System.out.println(a1);
      Arrays.sort(b1);
      if(Arrays.equals(a1, b1)){
          System.out.println("This is an anagram");
      }
      else{
          System.out.println("This is not an anagram");
      }
    }

    public static void anagraming2(String source,String target){
	    boolean anagram = Stream.of(source
        .toLowerCase()
                .split("")).sorted().collect(Collectors.joining())
                .equals(Stream.of(target.toLowerCase().split("")).sorted()
                        .collect(Collectors.joining()));

        System.out.println("Is it an anagram? "+anagram);

    }


}
