package algorithm;


import org.assertj.core.util.Streams;

import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DeleteCharFromString {

	public static String removeCharecter(String word, char remove){
	    StringBuilder sb = new StringBuilder();
	    char [] ch = word.toCharArray();
	    for(Character c: ch){
	        if(!c.equals(remove)){
	            sb.append(c);

            }
        }
	   return sb.toString();
    }

    public static void removeCharecter2(String str, char removeChar) {

        Stream.of(str.split(""))
                .filter(s-> !s.equals(String.valueOf(removeChar)))
                .collect(Collectors.toList())
               .forEach(System.out::print);

    }

	
	public static void main(String[] args) {
		String string = "Tawfiq";
		System.out.println(removeCharecter(string,'w'));
        removeCharecter2(string,'t');
        System.out.println();
		
		StringBuilder word = new StringBuilder();
		word.append("1");
		word.append("2");
		word.append("345678");
		System.out.println(word);
		word.delete(1, 4);
		System.out.println(word);

		
	}
}
