package practice;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class JavaStreams {

    private String name;
    private String gender;




    @Override
    public String toString() {
        return "JavaStreams{" +
                "name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                '}';
    }

    public String getName() {
        return name;
    }

    public String setName(String name) {
        this.name = name;
        return name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public static void main(String[] args) {
        List<String> names = Arrays.asList("<PERSON>","<PERSON>","<PERSON>");
        names.stream().
                filter(name->name.startsWith("M"))
                .collect(Collectors.toList())
                .forEach(System.out::println);

        JavaStreams s = new JavaStreams();
        List<String> t1 = new ArrayList<>();
        s.setName("<PERSON>");
        t1.add(s.getName());
        t1.add(s.setName("Mike"));
        System.out.println(t1);


        int number1 = incrementbyone.apply(4);
        int number2 = multiplybyten.apply(10);
        System.out.println("Number1 is: "+number1+"\n"+"Number2 is: "+number2);
        System.out.println("This is supplier function: "+namesupplier.get());

        //Joining two functions
        Function<Integer,Integer> addthenmultipy = incrementbyone.andThen(multiplybyten);
        System.out.println(addthenmultipy.apply(4));

        System.out.println(phonenumber.test("190"));
    }

    static Function<Integer,Integer> incrementbyone = number->number+1;
    static Function<Integer,Integer> multiplybyten = number->number*10;

    static Predicate<String> phonenumber = number ->
            number.startsWith("1") && number.endsWith("0");
    static Supplier<List<String>> namesupplier = ()->
            Arrays.asList(
                    "Mike",
                    "Tom"
            );
}
