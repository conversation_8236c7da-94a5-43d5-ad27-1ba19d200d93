package structures;

import java.util.HashMap;
import java.util.Map;

public class MapOfMap {

	public static void main(String[] args) {
		
		Map<String, Map<String, String>> students = new HashMap<>();
		Map<String, String> student = null;
		
		student = new HashMap<>();
		student.put("Name", "<PERSON><PERSON><PERSON>");
		student.put("Phone", "5162339429");
		student.put("Address", "397 Plainfield Ave, Floral Park, NY 11001");

		students.put("5162339429", student);
		
		student = new HashMap<>();
		student.put("Name", "Ameera");
		student.put("Phone", "5162339428");
		student.put("Address", "397 Plainfield Ave, Floral Park, NY 11001");

		students.put("5162339428", student);
		
		student = new HashMap<>();
		student.put("Name", "<PERSON><PERSON><PERSON><PERSON>");
		student.put("Phone", "5162339427");
		student.put("Address", "397 Plainfield Ave, Floral Park, NY 11001");

		students.put("5162339427", student);
		
		System.out.println(students.get("5162339428").get("Name"));
		
	}

}
