package structures;

import java.util.Comparator;
import java.util.Iterator;
import java.util.PriorityQueue;

public class PriorityQueueTest {

	public static void main(String[] args) {
		// Creating empty priority queue 
        PriorityQueue<String> pQueue = new PriorityQueue<String>();
     // Adding items to the pQueue using add() 
        pQueue.add("A"); 
        pQueue.add("Z"); 
        pQueue.add("C"); 
        pQueue.add("B"); 
        pQueue.add("C"); 
        pQueue.add("A"); 
        
        
        // Printing all elements 
        System.out.println("The queue elements:");
        Iterator itr = pQueue.iterator();
        while (itr.hasNext()) 
            System.out.println(itr.next());
        
        
        // Create a Priority Queue
        PriorityQueue<Integer> numbers = new PriorityQueue<>();

        // Add items to a Priority Queue (ENQUEUE)
        numbers.add(750);
        numbers.add(500);
        numbers.add(900);
        numbers.add(100);
        numbers.add(50);
        numbers.add(900);
        numbers.add(500);

        // Remove items from the Priority Queue (DEQUEUE)
        while (!numbers.isEmpty()) {
            System.out.println(numbers.remove());
        }

        // Create a Priority Queue
        PriorityQueue<String> namePriorityQueue = new PriorityQueue<>();

        // Add items to a Priority Queue (ENQUEUE)
        namePriorityQueue.add("Lisa");
        namePriorityQueue.add("Robert");
        namePriorityQueue.add("John");
        namePriorityQueue.add("Chris");
        namePriorityQueue.add("Angelina");
        namePriorityQueue.add("C");
        namePriorityQueue.add("Joe");
        namePriorityQueue.add("Ca");

        // Remove items from the Priority Queue (DEQUEUE)
        while (!namePriorityQueue.isEmpty()) {
            System.out.println(namePriorityQueue.remove());
        }

        
        // A custom comparator that compares two Strings by their length.
        Comparator<String> stringLengthComparator = new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                return s1.length() - s2.length();
            }
        };
        
        System.out.println("A custom comparator that compares two Strings by their length.");
     // Create a Priority Queue
        PriorityQueue<String> namePriorityQueue2 = new PriorityQueue<>(stringLengthComparator);

        // Add items to a Priority Queue (ENQUEUE)
        namePriorityQueue2.add("Lisa");
        namePriorityQueue2.add("Robert");
        namePriorityQueue2.add("John");
        namePriorityQueue2.add("Chris");
        namePriorityQueue2.add("Angelina");
        namePriorityQueue2.add("C");
        namePriorityQueue2.add("Joe");
        namePriorityQueue2.add("Ca");

        // Remove items from the Priority Queue (DEQUEUE)
        while (!namePriorityQueue2.isEmpty()) {
            System.out.println(namePriorityQueue2.remove());
        }

	}

}
