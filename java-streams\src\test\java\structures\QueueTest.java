package structures;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.Queue;

public class QueueTest {

	public static void main(String[] args) {
		// Create and initialize a Queue using a LinkedList
        Queue<String> waitingQueue = new LinkedList<>();
        
        // Adding new elements to the Queue (The Enqueue operation)
        waitingQueue.add("<PERSON>ee<PERSON>");
        waitingQueue.add("<PERSON>");
        waitingQueue.add("<PERSON>");
        waitingQueue.add("Mark");
        waitingQueue.add("<PERSON>");

        System.out.println("WaitingQueue : " + waitingQueue);
        
        // The remove() method throws NoSuchElementException if the Queue is empty
        String name = waitingQueue.remove();
        System.out.println("Removed from WaitingQueue : " + name + " | New WaitingQueue : " + waitingQueue);

        // Removing an element from the Queue using poll()
        // The poll() method is similar to remove() except that it returns null if the Queue is empty.
        name = waitingQueue.poll();
        System.out.println("Removed from WaitingQueue : " + name + " | New WaitingQueue : " + waitingQueue);
        
        // Check is a Queue is empty
        System.out.println("is waitingQueue empty? : " + waitingQueue.isEmpty());

        // Find the size of the Queue
        System.out.println("Size of waitingQueue : " + waitingQueue.size());

        // Check if the Queue contains an element
        name = "Mark";
        if(waitingQueue.contains(name)) {
            System.out.println("WaitingQueue contains " + name);
        } else {
            System.out.println("Waiting Queue doesn't contain " + name);
        }
        
        // Get the element at the front of the Queue without removing it using element()
        // The element() method throws NoSuchElementException if the Queue is empty
        String firstPersonInTheWaitingQueue =  waitingQueue.element();
        System.out.println("First Person in the Waiting Queue (element()) : " + firstPersonInTheWaitingQueue);

        // Get the element at the front of the Queue without removing it using peek()
        // The peek() method is similar to element() except that it returns null if the Queue is empty
        firstPersonInTheWaitingQueue = waitingQueue.peek();
        System.out.println("First Person in the Waiting Queue : " + firstPersonInTheWaitingQueue);
        System.out.println("WaitingQueue : " + waitingQueue);
        
        
        
        System.out.println("\n=== Iterating over a Queue using iterator() ===");
        Iterator<String> waitingQueueIterator = waitingQueue.iterator();
        while (waitingQueueIterator.hasNext()) {
            name = waitingQueueIterator.next();
            System.out.println(name);
        }
        System.out.println("WaitingQueue : " + waitingQueue);
        
        System.out.println("\n=== Iterating over a Queue using simple for-each loop ===");
        for(String na: waitingQueue) {
            System.out.println(na);
        }
        System.out.println("WaitingQueue : " + waitingQueue);
        
        ((LinkedList)waitingQueue).remove(1);
        System.out.println("WaitingQueue : " + waitingQueue);
        
	}

}
