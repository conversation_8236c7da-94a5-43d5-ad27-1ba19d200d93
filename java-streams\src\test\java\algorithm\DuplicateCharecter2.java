package algorithm;

import org.junit.jupiter.api.RepeatedTest;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DuplicateCharecter2 {
	
	public static void countDupChars(String str){
        HashMap<Character,Integer> map = new HashMap<>();
        char [] c = str.toCharArray();
        for(Character ch: c){
            if(map.containsKey(c)){
                map.put(ch,map.get(ch)+1);

            }
            else{
                map.put(ch, 1);
            }
            Set<Character> set = map.keySet();
            for(Character s: set){
                if(map.get(s)>1){
                    System.out.println("This is a duplicate: "+ s + " count:"+ map.get(s));
                }
            }
        }


	    }

    public void countDupWords(String str){

        //Create a HashMap
        HashMap<Object, Integer> map = new HashMap<Object, Integer>();

        //Convert the String to char array
        String[] chars = str.split(" ");

        /* logic: char are inserted as keys and their count
         * as values. If map contains the char already then
         * increase the value by 1
         */
        for(String ch:chars){
            if(map.containsKey(ch)){
                map.put(ch, map.get(ch)+1);
            } else {
                map.put(ch, 1);
            }
        }

        //Obtaining set of keys
        Set<Object> keys = map.keySet();

        /* Display count of chars if it is
         * greater than 1. All duplicate chars would be
         * having value greater than 1.
         */
        for(Object ch:keys){
            if(map.get(ch) > 1){
                System.out.println("Char "+ch+" "+map.get(ch));
            }
        }
    }

	    public static void duplicateword(String str){
            List<String> list = Stream.of(str).map(w -> w.split("\\s+")).flatMap(Arrays::stream)
                    .collect(Collectors.toList());

            Map <String, Integer > wordCounter = list.stream()
                    .collect(Collectors.toMap(String::toLowerCase, w -> 1, Integer::sum));

            System.out.println(wordCounter);
        }

    public static void duplicatechar(String str){
        List<String> list = Stream.of(str).map(w -> w.replace(" ", "").split("")).flatMap(Arrays::stream)
                .collect(Collectors.toList());

        Map <String, Integer > wordCounter = list.stream()
                .collect(Collectors.toMap(String::toLowerCase, w -> 1, Integer::sum));

        System.out.println(wordCounter);
    }
	
	public static void main(String a[]){
	    duplicateword("This is the same as this is right time as is this");
        countDupChars("This is the same as this is right time as is this");
		DuplicateCharecter2 obj = new DuplicateCharecter2();
        System.out.println("String: BeginnersBook.com");
        System.out.println("-------------------------");
        obj.countDupChars("BeginnersBook.com");
        obj.countDupWords("This This is is Sparta");

//        System.out.println("\nString: ChaitanyaSingh");
//        System.out.println("-------------------------");
//        obj.countDupChars("ChaitanyaSingh");
//
//        System.out.println("\nString: #@$@!#$%!!%@");
//        System.out.println("-------------------------");
//        obj.countDupChars("#@$@!#$%!!%@");
    }

  
}
