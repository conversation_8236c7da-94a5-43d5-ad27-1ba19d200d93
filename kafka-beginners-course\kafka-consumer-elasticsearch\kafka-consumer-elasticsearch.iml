<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.elasticsearch.client:elasticsearch-rest-high-level-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch-core:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch-secure-sm:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch-x-content:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.17" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.8.11" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-smile:2.8.11" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.8.11" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.8.11" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch-geo:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-core:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-analyzers-common:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-backward-codecs:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-grouping:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-highlighter:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-join:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-memory:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-misc:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-queries:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-queryparser:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-sandbox:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-spatial:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-spatial-extras:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-spatial3d:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-suggest:8.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch-cli:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: net.sf.jopt-simple:jopt-simple:5.0.2" level="project" />
    <orderEntry type="library" name="Maven: com.carrotsearch:hppc:0.8.1" level="project" />
    <orderEntry type="library" name="Maven: joda-time:joda-time:2.10.3" level="project" />
    <orderEntry type="library" name="Maven: com.tdunning:t-digest:3.2" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:jna:4.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch.client:elasticsearch-rest-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpasyncclient:4.1.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore-nio:4.4.12" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.11" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch.plugin:mapper-extras-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch.plugin:parent-join-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch.plugin:aggs-matrix-stats-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch.plugin:rank-eval-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch.plugin:lang-mustache-client:7.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.spullara.mustache.java:compiler:0.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.kafka:kafka-clients:2.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.lz4:lz4-java:1.4.1" level="project" />
    <orderEntry type="library" name="Maven: org.xerial.snappy:snappy-java:1.1.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-simple:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.5" level="project" />
  </component>
</module>