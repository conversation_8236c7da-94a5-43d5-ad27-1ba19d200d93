package algorithm;

public class ReverseString {

	public static String reverseString(String str) {

	    char [] ch = str.toCharArray();
	    int left;
	    int right = ch.length-1;


	    for(left=0;left<right;right--,left++){
	        char temp = ch[right];
	        ch[right] = ch[left];
	        ch[left] = temp;
        }
	   return String.valueOf(ch);
    }

	public static void main(String[] args) {
        System.out.println(reverseString("Sparta"));

		
//		StringBuffer sb = new StringBuffer("ToyStory4");
//		StringBuffer reverseStr = sb.reverse();
//		System.out.println(reverseStr);
		
		
				
	}
}
