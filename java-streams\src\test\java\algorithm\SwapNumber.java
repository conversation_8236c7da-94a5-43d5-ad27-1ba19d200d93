package algorithm;

public class SwapNumber {
	

    public static void main(String[]args){
      SwapNumber swap = new SwapNumber();
      swap.swapped(7,9);

    }

    public void swapped (int number1, int number2){
        System.out.println("This is the number before swap: Number 1: "+number1+""+" Number 2: "+number2+"");
        int temp;
        temp = number1;
        number1 = number2;
        number2 = temp;

        System.out.println("Number 1: "+number1+""+" Number 2: "+number2+"");
    }
}
