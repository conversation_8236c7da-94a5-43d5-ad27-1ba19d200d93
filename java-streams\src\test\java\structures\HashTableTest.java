package structures;

import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;
import java.util.Set;


public class HashTableTest {

	public static void main(String[] args) {
	
		//Hashtable<String, String> numberMapping = new Hashtable<>();
		
		// Creating a HashMap
        Map<String, Integer> numberMapping = new Hashtable<>();

        // Adding key-value pairs to a HashMap
        numberMapping.put("One", 1);
        numberMapping.put("Two",5);
        numberMapping.put("Three", 3);
        numberMapping.put("Ivaan",5 ); 

        // Add a new key-value pair only if the key does not exist in the HashMap, or is mapped to `null`
        //numberMapping.putIfAbsent("Four", 4);

        System.out.println(numberMapping);
        System.out.println(numberMapping.get("Two"));
        
        if(numberMapping.containsKey("Ivaan")) {
        	numberMapping.remove("Ivaan");
        }
        System.out.println(numberMapping);
        
        System.out.println("Count: " + numberMapping.size());
        //numberMapping.clear();
        System.out.println(numberMapping);
        
     // Check if a HashMap is empty
        System.out.println("is empty? : " + numberMapping.isEmpty());

        
        // Add a new key-value pair only if the key does not exist in the HashMap, or is mapped to `null`
        numberMapping.putIfAbsent("Four", 4);
        numberMapping.putIfAbsent("Four", 4);
        if(!numberMapping.containsKey("Four")) {
        	numberMapping.put("Four", 4);
        }
        numberMapping.putIfAbsent("Four+", 4);
        
        System.out.println(numberMapping);
        
        // Check if a value exists in a HashMap
        if(numberMapping.containsValue(4)) {
            System.out.println("There is a value 4");
        } else {
            System.out.println("There is no value 4");
        }
        
        // HashMap's key set
        Set<String> keys = numberMapping.keySet();
        System.out.println("Keys : " + keys);
        for(String key : keys) {
        	System.out.println( "Key: " + key + " Value: " +  numberMapping.get(key));
        }
        
        
        Map<Integer, Employee> employeesMap = new HashMap<>();

        employeesMap.put(1001, new Employee(1001, "Rajeev", "Bengaluru"));
       
        Employee e2 = new Employee(1002, "David", "New York");
        employeesMap.put(e2.getId(),e2 );
       
        Employee e3 = new Employee(1003, "Jack", "Paris");
        employeesMap.put(e3.getId(),e3);

        System.out.println(employeesMap);
        
        Employee employee;
        for(int i = 200; i < 210; i++) {
        	employee = new Employee(i, "David - " + i, "Jack");
        	employeesMap.put(employee.getId(),employee);
        }
        
        System.out.println(employeesMap);
        
	}

}
