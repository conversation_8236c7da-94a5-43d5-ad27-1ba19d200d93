package imperative;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static imperative.Main.Gender.FEMALE;
import static imperative.Main.Gender.MALE;

public class Main {
    public static void main(String[] args) {
        List<Person> people = Arrays.asList(
            new Person("<PERSON>", MALE),
            new Person("<PERSON>", FEMALE),
            new Person("<PERSON><PERSON>", FEMALE),
            new Person("<PERSON>", MALE),
            new Person("<PERSON>", FEMALE)
        );
        people.forEach(System.out::println);

        System.out.println("// Imperative approach");
        // Imperative approach

        List<Person> females = new ArrayList<>();

        for (Person person : people) {
            if (FEMALE.equals(person.gender)) {
                females.add(person);
            }
        }

        for (Person female : females) {
            System.out.println(female);
        }

        System.out.println("// Declarative approach");
        // Declarative approach

        //Predicate logic only used as boolean (returning true or false)
        Predicate<Person> personPredicate = person -> MALE.equals(person.gender);

        List<Person> females2 = people.stream()
                .filter(personPredicate)
                .collect(Collectors.toList());
        females2.forEach(System.out::println);
    }

      static class Person {

          private final String name;
          private final Gender gender;

        Person(String name, Gender gender) {
            this.name = name;
            this.gender = gender;
        }

        @Override
        public String toString() {
            return "Person{" +
                    "name='" + name + '\'' +
                    ", gender=" + gender +
                    '}';
        }


     }

    enum Gender {
        MALE, FEMALE
    }
}
