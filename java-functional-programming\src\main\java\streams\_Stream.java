package streams;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.function.IntConsumer;
import java.util.function.Predicate;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

import static streams._Stream.Gender.*;

public class _Stream {

    public static void main(String[] args) {

        List<Person> people = Arrays.asList(
                new Person("<PERSON>", MALE),
                new Person("<PERSON>", FEMALE),
                new Person("<PERSON><PERSON>", FEMALE),
                new Person("<PERSON>", MALE),
                new Person("<PERSON>", FEMALE),
                new Person("<PERSON>", PREFER_NOT_TO_SAY)
        );

        people.stream()
                .map(person -> person.name)
                .mapToInt(String::length)
                .forEach(System.out::println);
        people.stream()
                .filter(name->name.gender.equals(MALE))
                .forEach(System.out::println);
        people.stream()
                .map(person->person.gender)
                .collect(Collectors.toSet())
                .forEach(System.out::println);
       boolean b = people.stream()
                .anyMatch(person->person.gender.equals(FEMALE))
                ;
        System.out.println(b);

    }

    static class Person {
        private final String name;
        private final Gender gender;

        Person(String name, Gender gender) {
            this.name = name;
            this.gender = gender;
        }

        @Override
        public String toString() {
            return "Person{" +
                    "name='" + name + '\'' +
                    ", gender=" + gender +
                    '}';
        }
    }

    enum Gender {
        MALE, FEMALE, PREFER_NOT_TO_SAY
    }
}
