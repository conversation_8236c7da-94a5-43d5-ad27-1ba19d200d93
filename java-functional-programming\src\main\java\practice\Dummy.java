package practice;

import java.util.HashMap;
import java.util.Set;

public class Dummy {
    public static void main(String[] args) {
        reverseString("Belen");

        String value = "This is testing Program testing Program Program";

        String item[] = value.split(" ");

        HashMap<String, Integer> map = new HashMap<>();

        for (String t : item) {
            if (map.containsKey(t)) {
                map.put(t, map.get(t) + 1);

            } else {
                map.put(t, 1);
            }
        }
        for (String key : map.keySet()) {
            System.out.print(key+": ");
            System.out.println(map.get(key));
        }
    }
    static void reverseString(String name){
        char [] c = name.toCharArray();
        StringBuilder sb = new StringBuilder();
        int firstLetter= 0;
        int lastLetter=c.length-1;
        for(int i=lastLetter; i>=firstLetter;i--){
            if(i==lastLetter){
                sb.append(Character.toUpperCase(c[i]));
            }
            else if(i==firstLetter){
                sb.append(Character.toLowerCase(c[i]));
            }
            else{
                sb.append(c[i]);
            }

        }
        System.out.println(sb);
    }
}
