package algorithm;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class JoinTwoList {
    public static void main(String[] args) {
        List<Integer> list1 = Arrays.asList(1,2,7,5);
        Arrays.sort(list1.toArray());
        System.out.println(list1);
        List<Integer> list2 = Arrays.asList(3,8,6);
        JoinTwoList j = new JoinTwoList();
        j.joiner(list1, list2);

    }
    public void joiner (List<Integer> list1,List<Integer> list2){
        List<Integer>s = Stream.concat(list1.stream(), list2.stream()).sorted().collect(Collectors.toList());
        System.out.println(s);
    }

}
