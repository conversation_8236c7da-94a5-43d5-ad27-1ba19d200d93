package algorithm;

public class DuplicateCharacters {

	public static void main(String[] args) {
		String str = "find the duplicates here";

		String characters = "";
		String duplicates = "";
		for (int i = 0; i < str.length(); i++) {

			String current = Character.toString(str.charAt(i));
			
			if (characters.contains(current)) {
				if (!duplicates.contains(current)) {

					duplicates += current;

					if (duplicates.contains(" ")) {
						duplicates = duplicates.replaceAll(" ", "");
					}
				}
			}
			characters += str.charAt(i);
		}
		System.out.println(duplicates);
		System.out.println("Count: " + duplicates.length());

	}

}
