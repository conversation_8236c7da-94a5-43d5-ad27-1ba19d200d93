import com.google.gson.internal.bind.util.ISO8601Utils;
import org.w3c.dom.ls.LSOutput;

import javax.swing.*;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.function.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public class PracticeStreams {

    public static void main(String[] args) {
        PracticeStreams c = new PracticeStreams();
        c.odd.apply(new int[]{1, 2, 3});
        c.even.apply(new int[]{1, 2, 3,4});
        if(c.phonenumbercheck.test("01989898")){
            System.out.println("This is a valid number");
        }
        else{
            System.out.println("This is not a valid number");
        }
        System.out.println("The name is: "+ c.names.get().get(1));
        c.distinct.apply(Arrays.asList(1,2,3,3,4,5,5,6));
        System.out.println();
        c.distinct2.apply(Arrays.asList(1,2,7,7,4,5,5,6));

        //Stream.iterate(initial value, next value)
        Stream.iterate(0, n -> n + 1)
                .limit(10)
                .forEach(System.out::println);

        IntStream.range(1, 10)
                .filter(n->n%2==1)
                .forEach(System.out::println);

        List<String> names = Arrays.asList("Tom","Dick","Harry");
        String newname = names.stream()
                .map(name->name.substring(0,1).toLowerCase()+name.substring(1))
                .collect(Collectors.joining("~"))
                ;
        System.out.println(newname);
    }

    Function<int[],int[]> odd = number ->
    {
        Arrays.stream(number)
                .filter(n->n%2==1)
                .forEach(s-> System.out.println("The odd numbers are: "+s));
        return number;
    };
    Function<int[],int[]> even = number ->
    {
        Arrays.stream(number)
                .filter(n->n%2==0)
                .forEach(s-> System.out.println("The even numbers are: "+s));
        return number;
    };

    Predicate<String> phonenumbercheck = phonenumber->
          phonenumber.contains("1")
                  && phonenumber.startsWith("0")
                    && phonenumber.endsWith("9")
                    && phonenumber.length()==9;

    Supplier<List<String>> names = ()->
            Arrays.asList("John","Sam","Tom");


    Function<List<Integer>,List<Integer>> distinct = lists -> {
        Arrays.stream(lists.toArray())
                .distinct()
                .forEach(System.out::print);
        return lists;
    };
    Function<List<Integer>,List<Integer>> distinct2 = lists -> {
        Arrays.stream(lists.toArray())
                .collect(Collectors.toSet())
                .forEach(System.out::print);
        return lists;
    };
    }



