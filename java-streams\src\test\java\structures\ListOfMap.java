package structures;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListOfMap {

	public static void main(String[] args) {
		
		List<Map<String, String>> students = new ArrayList<>();
		Map<String, String> student = null;
		
		student = new HashMap<>();
		student.put("Name", "<PERSON>tekhar");
		student.put("Phone", "5162339429");
		student.put("Address", "397 Plainfield Ave, Floral Park, NY 11001");

		students.add(student);
		
		student = new HashMap<>();
		student.put("Name", "Ameera");
		student.put("Phone", "5162339428");
		student.put("Address", "397 Plainfield Ave, Floral Park, NY 11001");

		students.add(student);
		
		student = new HashMap<>();
		student.put("Name", "<PERSON><PERSON><PERSON>hn");
		student.put("Phone", "5162339427");
		student.put("Address", "397 Plainfield Ave, Floral Park, NY 11001");

		students.add(student);
		
		System.out.println(students.get(1).get("Name"));
		
	}

}
