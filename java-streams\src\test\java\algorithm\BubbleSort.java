package algorithm;

import java.util.Arrays;

public class BubbleSort {
	
public static int[] bubbleSort (int[] number){
    for(int i=0; i<number.length-1; i++){
        for(int j=0; j<number.length-i-1; j++){
            if(number[j]>number[j+1]){
                int temp = number[j];
                number[j]=number[j+1];
                number[j+1]=temp;
            }
        }
    }
    return number;
}
	
	
	public static void main(String[] args) {
		int[] numbers = {5,6,1,9,3};
		System.out.println(Arrays.toString(numbers));
		
		System.out.println("########## Before Sorted #############");
		System.out.println(Arrays.toString(numbers));
		System.out.println("*************** Sorted here ********************");
		int[] sorted = bubbleSort(numbers);
		System.out.println(Arrays.toString(sorted));
		
	}
	
	

}
