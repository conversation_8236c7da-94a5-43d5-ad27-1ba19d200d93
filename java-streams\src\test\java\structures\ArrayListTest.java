package structures;

import java.util.Arrays;
import java.util.List;
import java.util.Vector;

public class ArrayListTest {

	public static void main(String[] args) {
		
		List<String> names = null;
		//names = new ArrayList<>();
		names = new Vector<>();
		
		names.add("Ivaan");
		//names.add(299);
		names.add("Ameera");
		names.add("Simrohn");
		names.add("Ameera");
		
		names.add(1, "Shehla");
		
		names.set(0, "Iftekhar");
		
		names.removeIf(name -> name.equalsIgnoreCase("Ameera"));
		System.out.println(names);
		
		names.remove("Ameera");
		System.out.println(names.get(1));
		
		System.out.println(names.size());
		
		//names.remove(1);
		
		System.out.println(names.size());
		
		//names.clear();
		
		System.out.println(names.size());

        for (String name : names) {
            System.out.println(name);
        }
		
		for(String item: names) {
			System.out.println(item);
		}
		
		System.out.println("*** Iterator ***");
        for (String name : names) {
            System.out.println(name);
        }
		
		
		
		 List<Integer> list = Arrays.asList(3,2,1,4,5,6,6);


		 //Array to ArrayList
		 Integer[] intArray = {3,2,1,4,5,6,6};
		 List<Integer> list2 = Arrays.asList(intArray);
		 for(Integer i:list2){
             System.out.println(i);
         }
		 
		 //ArrayList to Array
		 Integer[] intArray2 = new Integer[list2.size()];;
		 intArray2 = list2.toArray(intArray2);
		 
		
		
	}

}
